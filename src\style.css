* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    height: 100vh;
    background: 
        radial-gradient(ellipse at center, rgba(255,255,255,0.02) 0%, transparent 70%),
        linear-gradient(180deg, 
            rgba(0, 0, 0, 1) 0%,
            rgba(20, 20, 20, 0.95) 20%,
            rgba(40, 40, 40, 0.9) 40%,
            rgba(60, 60, 60, 0.85) 60%,
            rgba(80, 80, 80, 0.8) 80%,
            rgba(102, 102, 102, 0.75) 100%);
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth;
}

#starCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.content {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: white;
    text-align: center;
    font-family: 'Arial', sans-serif;
    user-select: none;
}

.second-page {
    background: 
        linear-gradient(180deg, 
            rgba(102, 102, 102, 0.75) 0%,
            rgba(120, 120, 120, 0.7) 20%,
            rgba(140, 140, 140, 0.65) 40%,
            rgba(160, 160, 160, 0.6) 60%,
            rgba(180, 180, 180, 0.55) 80%,
            rgba(200, 200, 200, 0.5) 100%);
}

.third-page {
    background: 
        linear-gradient(180deg, 
            rgba(200, 200, 200, 0.5) 0%,
            rgba(180, 180, 180, 0.55) 20%,
            rgba(160, 160, 160, 0.6) 40%,
            rgba(140, 140, 140, 0.65) 60%,
            rgba(120, 120, 120, 0.7) 80%,
            rgba(102, 102, 102, 0.75) 100%);
}

.section-title {
    font-size: 2.5rem;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.section-text {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
    max-width: 600px;
    line-height: 1.6;
}

.title {
    font-size: 6rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
}

/* 滚动指示器样式 */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: translateX(-50%) translateY(-5px);
}

.scroll-arrow span {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 300;
    letter-spacing: 1px;
}

.arrow-down {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}

.arrow-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
    animation: arrowPulse 2s infinite ease-in-out;
}

.arrow-head {
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid rgba(255, 255, 255, 0.8);
    border-bottom: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounce 2s infinite ease-in-out;
}

/* 向上箭头样式 */
.arrow-up {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}

.arrow-up .arrow-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
    animation: arrowPulse 2s infinite ease-in-out;
}

.arrow-head-up {
    position: absolute;
    top: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-left: 2px solid rgba(255, 255, 255, 0.8);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounceUp 2s infinite ease-in-out;
}

@keyframes arrowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(0.8);
    }
    50% {
        opacity: 1;
        transform: scaleY(1);
    }
}

@keyframes arrowBounce {
    0%, 100% {
        transform: rotate(45deg) translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateY(3px);
        opacity: 1;
    }
}

@keyframes arrowBounceUp {
    0%, 100% {
        transform: rotate(45deg) translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateY(-3px);
        opacity: 1;
    }
}

/* 星座连线效果 */
.constellation {
    position: absolute;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 1;
    fill: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }
    .subtitle {
        font-size: 1rem;
    }
    
    .scroll-indicator {
        bottom: 30px;
    }
    
    .scroll-arrow span {
        font-size: 12px;
    }
} 
